# Container Configuration Guide for Video Quality Script

## 🐳 JSON Configuration for Containers

Since you're running from a remote host with a container agent, the script now supports JSON-based configuration which is perfect for containerized environments.

## 📁 Configuration File Options

### 1. **Automatic Config File Detection**
The script automatically looks for configuration files in this order:
1. `config.json` (current directory)
2. `video_quality_config.json` (current directory)
3. `/app/config.json` (container standard location)

### 2. **Create Sample Configuration**
```bash
# Generate a sample config file
python top_level_videoquality.py --create-config
```

This creates a `config.json` file with all available options.

## 📝 Configuration File Structure

### **Complete config.json Example:**
```json
{
    "ssh": {
        "host": "************",
        "username": "nvme",
        "password": "your_actual_password",
        "timeout": 60,
        "retries": 5,
        "keepalive": 30
    },
    "git": {
        "regression_scripts_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git",
        "video_ip_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git",
        "regression_cfg_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
    },
    "base_dir": "/mnt/ceph/Automation/quick_quality_test",
    "simulations_dir": "/mnt/ceph/Automation/simulations/*"
}
```

### **Minimal config.json (only override what you need):**
```json
{
    "ssh": {
        "password": "your_actual_password"
    }
}
```

## 🐳 Container Usage Patterns

### 1. **Docker Volume Mount**
```bash
# Mount config file into container
docker run -v /path/to/config.json:/app/config.json your-image
```

### 2. **Docker Compose**
```yaml
version: '3.8'
services:
  video-quality:
    image: your-image
    volumes:
      - ./config.json:/app/config.json
      - ./logs:/app/logs
    command: python top_level_videoquality.py -j job1 -c1 config1.cfg
```

### 3. **Kubernetes ConfigMap**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: video-quality-config
data:
  config.json: |
    {
      "ssh": {
        "host": "************",
        "username": "nvme",
        "password": "your_password"
      }
    }
---
apiVersion: v1
kind: Pod
metadata:
  name: video-quality-pod
spec:
  containers:
  - name: video-quality
    image: your-image
    volumeMounts:
    - name: config-volume
      mountPath: /app/config.json
      subPath: config.json
  volumes:
  - name: config-volume
    configMap:
      name: video-quality-config
```

### 4. **Kubernetes Secret (Recommended for passwords)**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: video-quality-secret
type: Opaque
data:
  config.json: <base64-encoded-config>
---
apiVersion: v1
kind: Pod
metadata:
  name: video-quality-pod
spec:
  containers:
  - name: video-quality
    image: your-image
    volumeMounts:
    - name: secret-volume
      mountPath: /app/config.json
      subPath: config.json
  volumes:
  - name: secret-volume
    secret:
      secretName: video-quality-secret
```

## 🔧 Configuration Options Explained

### **SSH Configuration**
```json
"ssh": {
    "host": "************",        // SSH server hostname/IP
    "username": "nvme",            // SSH username
    "password": "your_password",   // SSH password
    "timeout": 60,                 // Connection timeout in seconds
    "retries": 5,                  // Number of retry attempts
    "keepalive": 30               // Keepalive interval in seconds
}
```

### **Git Configuration**
```json
"git": {
    "regression_scripts_url": "https://...",  // Regression scripts repository
    "video_ip_url": "https://...",           // Video IP repository
    "regression_cfg_url": "https://..."      // Configuration repository
}
```

### **Directory Configuration**
```json
{
    "base_dir": "/mnt/ceph/Automation/quick_quality_test",  // Base working directory
    "simulations_dir": "/mnt/ceph/Automation/simulations/*" // Simulations directory
}
```

## 🔒 Security Best Practices

### 1. **For Development/Testing**
```json
{
    "ssh": {
        "password": "test_password"
    }
}
```

### 2. **For Production (using secrets)**
```bash
# Create config with placeholder
echo '{"ssh":{"password":"REPLACE_ME"}}' > config.json

# Replace at runtime in container
sed -i 's/REPLACE_ME/'$SSH_PASSWORD'/g' /app/config.json
```

### 3. **Using Environment Variables as Fallback**
The script still supports environment variables as a fallback:
```bash
export SSH_PASSWORD="your_password"
# Script will use env vars if no config file found
```

## 📋 Usage Examples

### **1. Basic Usage with Config File**
```bash
# Make sure config.json exists in current directory
python top_level_videoquality.py -j job1 -c1 config1.cfg
```

### **2. Container with Custom Config Location**
```bash
# If config is at /app/config.json (standard container location)
docker run -v /host/config.json:/app/config.json your-image \
  python top_level_videoquality.py -j job1 -c1 config1.cfg
```

### **3. Generate Config Template**
```bash
# Create a template config file
python top_level_videoquality.py --create-config
# Edit the generated config.json file
nano config.json
```

## 🔄 Migration from Environment Variables

### **Old Way (Environment Variables):**
```bash
export SSH_HOST="************"
export SSH_USER="nvme"
export SSH_PASSWORD="password"
python top_level_videoquality.py -j job1 -c1 config1.cfg
```

### **New Way (JSON Config):**
```bash
# Create config.json
echo '{
  "ssh": {
    "host": "************",
    "username": "nvme", 
    "password": "password"
  }
}' > config.json

# Run script (automatically detects config.json)
python top_level_videoquality.py -j job1 -c1 config1.cfg
```

## 🚀 Container Agent Integration

### **1. Jenkins Pipeline**
```groovy
pipeline {
    agent {
        docker {
            image 'your-video-quality-image'
        }
    }
    stages {
        stage('Setup Config') {
            steps {
                writeFile file: 'config.json', text: """
                {
                    "ssh": {
                        "password": "${SSH_PASSWORD}"
                    }
                }
                """
            }
        }
        stage('Run Quality Test') {
            steps {
                sh 'python top_level_videoquality.py -j ${BUILD_NUMBER} -c1 config1.cfg'
            }
        }
    }
}
```

### **2. GitLab CI**
```yaml
video_quality_test:
  image: your-video-quality-image
  before_script:
    - echo '{"ssh":{"password":"'$SSH_PASSWORD'"}}' > config.json
  script:
    - python top_level_videoquality.py -j $CI_JOB_ID -c1 config1.cfg
  variables:
    SSH_PASSWORD: $SSH_PASSWORD  # Set in GitLab CI variables
```

### **3. GitHub Actions**
```yaml
name: Video Quality Test
on: [push]
jobs:
  test:
    runs-on: ubuntu-latest
    container: your-video-quality-image
    steps:
    - uses: actions/checkout@v2
    - name: Create config
      run: |
        echo '{"ssh":{"password":"${{ secrets.SSH_PASSWORD }}"}}' > config.json
    - name: Run test
      run: python top_level_videoquality.py -j ${{ github.run_number }} -c1 config1.cfg
```

## ✅ Benefits of JSON Configuration

1. **🐳 Container-Friendly**: Easy to mount as volumes or configmaps
2. **🔧 Flexible**: Override only the settings you need
3. **📝 Version Control**: Can be tracked in git (without sensitive data)
4. **🔒 Secure**: Supports secrets management in orchestration platforms
5. **🔄 Backward Compatible**: Still supports environment variables as fallback
6. **📋 Self-Documenting**: JSON structure is clear and readable

## 🆘 Troubleshooting

### **Config File Not Found**
```bash
# Check if config file exists
ls -la config.json

# Create sample config
python top_level_videoquality.py --create-config
```

### **Permission Issues**
```bash
# Make sure config file is readable
chmod 644 config.json
```

### **JSON Syntax Errors**
```bash
# Validate JSON syntax
python -m json.tool config.json
```

### **Container Path Issues**
```bash
# Check if config is mounted correctly in container
docker exec -it container_name ls -la /app/config.json
```

This JSON-based configuration system is perfect for your container agent setup and provides much better security and flexibility than hardcoded credentials!
