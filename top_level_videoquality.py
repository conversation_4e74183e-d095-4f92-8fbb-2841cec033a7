import time
import sys
import argparse
import os
import re
import select
import csv
import datetime
import logging
from typing import Optional, Dict, Any, List, Tuple, Union
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path

import paramiko
from subprocess import getoutput
import json
from mysqlLib import mySQL
import pandas as pd


# Constants
DEFAULT_SSH_TIMEOUT = 60
DEFAULT_SSH_RETRIES = 5
DEFAULT_KEEPALIVE = 30
DEFAULT_JOB_CHECK_INTERVAL = 30
DEFAULT_SLEEP_TIME = 5
MAX_SLEEP_TIME = 60

# Configuration
@dataclass
class SSHConfig:
    """SSH connection configuration."""
    host: str
    username: str
    password: str
    timeout: int = DEFAULT_SSH_TIMEOUT
    retries: int = DEFAULT_SSH_RETRIES
    keepalive: int = DEFAULT_KEEPALIVE

@dataclass
class GitConfig:
    """Git repository configuration."""
    regression_scripts_url: str
    video_ip_url: str
    regression_cfg_url: str

@dataclass
class AppConfig:
    """Application configuration."""
    ssh: SSHConfig
    git: GitConfig
    base_dir: str = "/mnt/ceph/Automation/quick_quality_test"
    simulations_dir: str = "/mnt/ceph/Automation/simulations/*"


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scriptlog.txt'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter)


parser.add_argument('-j',
                    '--job',
                    type=str,
                    default="job1",
                    help='Provide the job name\n\n')
parser.add_argument('-b',
                    '--branch',
                    type=str,
                    default="main",
                    help='Provide the scripts branch name\n\n')
parser.add_argument('-g',
                    '--gaudibranch',
                    type=str,
                    default="RANGER_CM133_v6.0",
                    help='Provide the Gaudi compile branch name\n\n')
parser.add_argument('-f',
                    '--configbranch',
                    type=str,
                    default="master",
                    help='Provide the config files branch name\n\n')
parser.add_argument('-e',
                    '--enable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to enable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument('-d',
                    '--disable_defination',
                    type=str,
                    default="",
                    help='Provide a defination to disable, ex: add_definitions(-DNETINT_RANGER_RC_V1_TAV)\n\n')
parser.add_argument("--twopass", action="store_true", help="Run two pass test")
parser.add_argument("--custom_Gaudi", action="store_true", help="Run two pass test")
parser.add_argument("--save_to_db", action="store_true", help="To save data to DB")
parser.add_argument('-c',
                    '--compile_flag',
                    type=str,
                    default="",
                    help='Add compile flag\n\n')
parser.add_argument('-c1',
					'--config1',
					type=str,
					default="NONE.cfg",
                    help='Pass1 Config parameter\n\n')
parser.add_argument('-c2',
					'--config2',
					type=str,
					default="NONE.cfg",
                    help='Pass2 Config parameter\n\n')
parser.add_argument('-c3',
					'--config3',
					type=str,
					default="NONE.cfg",
                    help='Pass3 Config parameter\n\n')
parser.add_argument('-c4',
					'--config4',
					type=str,
					default="NONE.cfg",
                    help='Pass4 Config parameter\n\n')
parser.add_argument('-c5',
					'--config5',
					type=str,
					default="NONE.cfg",
                    help='Pass5 Config parameter\n\n')
parser.add_argument('-v',
					'--Compare_with',
					type=str,
					default="RANGER_CM_PX4_156_v2.0.0",
                    help='Provide branch name to compare with\n\n')
parser.add_argument('-i',
					'--config_file',
					type=str,
					default="BQTC_12_2024.cfg",
                    help='Provide config file to compare with\n\n')
parser.add_argument('--create-config',
                    action='store_true',
                    help='Create a sample configuration file and exit\n\n')
args = parser.parse_args()

# Handle config file creation
if args.create_config:
    create_sample_config()
    sys.exit(0)
compile_flag = args.compile_flag

def ensure_cfg_extension(config):
    if not config.endswith('.cfg'):
        return config + '.cfg'
    return config

config1 = ensure_cfg_extension(args.config1)
config2 = ensure_cfg_extension(args.config2)
config3 = ensure_cfg_extension(args.config3)
config4 = ensure_cfg_extension(args.config4)
config5 = ensure_cfg_extension(args.config5)

enable_defination = args.enable_defination
disable_defination = args.disable_defination


def load_config_from_json(config_file: str = 'config.json') -> AppConfig:
    """Load configuration from JSON file."""
    default_config = {
        "ssh": {
            "host": "************",
            "username": "nvme",
            "password": "logan",
            "timeout": DEFAULT_SSH_TIMEOUT,
            "retries": DEFAULT_SSH_RETRIES,
            "keepalive": DEFAULT_KEEPALIVE
        },
        "git": {
            "regression_scripts_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git",
            "video_ip_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git",
            "regression_cfg_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
        },
        "base_dir": "/mnt/ceph/Automation/quick_quality_test",
        "simulations_dir": "/mnt/ceph/Automation/simulations/*"
    }

    # Try to load from JSON file, fall back to defaults
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                # Merge user config with defaults
                config_data = {**default_config, **user_config}
                # Handle nested dictionaries
                if 'ssh' in user_config:
                    config_data['ssh'] = {**default_config['ssh'], **user_config['ssh']}
                if 'git' in user_config:
                    config_data['git'] = {**default_config['git'], **user_config['git']}
                logger.info(f"Configuration loaded from {config_file}")
        except Exception as e:
            logger.warning(f"Failed to load config from {config_file}: {e}. Using defaults.")
            config_data = default_config
    else:
        logger.info(f"Config file {config_file} not found. Using default configuration.")
        config_data = default_config

    # Create configuration objects
    ssh_config = SSHConfig(**config_data['ssh'])
    git_config = GitConfig(**config_data['git'])

    return AppConfig(
        ssh=ssh_config,
        git=git_config,
        base_dir=config_data['base_dir'],
        simulations_dir=config_data['simulations_dir']
    )


def create_sample_config(config_file: str = 'config.json') -> None:
    """Create a sample configuration file."""
    sample_config = {
        "ssh": {
            "host": "************",
            "username": "nvme",
            "password": "your_password_here",
            "timeout": 60,
            "retries": 5,
            "keepalive": 30
        },
        "git": {
            "regression_scripts_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git",
            "video_ip_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git",
            "regression_cfg_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
        },
        "base_dir": "/mnt/ceph/Automation/quick_quality_test",
        "simulations_dir": "/mnt/ceph/Automation/simulations/*"
    }

    try:
        with open(config_file, 'w') as f:
            json.dump(sample_config, f, indent=4)
        logger.info(f"Sample configuration created: {config_file}")
        print(f"\n📝 Sample configuration file created: {config_file}")
        print("Please edit this file with your actual credentials and settings.")
    except Exception as e:
        logger.error(f"Failed to create sample config: {e}")


def load_config() -> AppConfig:
    """Load configuration with fallback support."""
    # Try different config file locations
    config_files = ['config.json', 'video_quality_config.json', '/app/config.json']

    for config_file in config_files:
        if os.path.exists(config_file):
            return load_config_from_json(config_file)

    # If no config file found, try environment variables as fallback
    if any(os.getenv(var) for var in ['SSH_HOST', 'SSH_USER', 'SSH_PASSWORD']):
        logger.info("Using environment variables for configuration")
        ssh_config = SSHConfig(
            host=os.getenv('SSH_HOST', '************'),
            username=os.getenv('SSH_USER', 'nvme'),
            password=os.getenv('SSH_PASSWORD', 'logan')
        )

        git_config = GitConfig(
            regression_scripts_url="https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git",
            video_ip_url="https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git",
            regression_cfg_url="https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
        )

        return AppConfig(ssh=ssh_config, git=git_config)

    # Fall back to default configuration
    logger.info("Using default configuration")
    return load_config_from_json('nonexistent.json')  # Will use defaults


config = load_config()

def print_now(text: str) -> None:
    """Legacy function for backward compatibility. Use logger instead."""
    logger.info(text)

def count_passes() -> int:
    """Count the number of non-NONE pass values."""
    passes = 0
    for pass_value in [config1, config2, config3, config4, config5]:
        if pass_value != "NONE.cfg":
            passes += 1
    return passes

class SSHConnectionManager:
    """Manages SSH connections with proper error handling and retries."""

    def __init__(self, ssh_config: SSHConfig):
        self.config = ssh_config

    @contextmanager
    def get_connection(self):
        """Context manager for SSH connections."""
        ssh = None
        try:
            ssh = self._connect()
            if ssh:
                yield ssh
            else:
                raise ConnectionError("Failed to establish SSH connection")
        finally:
            if ssh:
                ssh.close()

    def _connect(self) -> Optional[paramiko.SSHClient]:
        """Establish SSH connection with retries."""
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        for attempt in range(self.config.retries):
            start_time = time.time()
            try:
                ssh.connect(
                    hostname=self.config.host,
                    username=self.config.username,
                    password=self.config.password,
                    timeout=self.config.timeout,
                    look_for_keys=False,
                    allow_agent=False,
                    banner_timeout=self.config.timeout
                )
                ssh.get_transport().set_keepalive(self.config.keepalive)
                logger.info(f"SSH connection established to {self.config.host}")
                return ssh
            except Exception as e:
                logger.warning(f"SSH connection attempt {attempt + 1}/{self.config.retries} failed: {e}")
                if attempt < self.config.retries - 1:
                    sleep_time = min(MAX_SLEEP_TIME - (time.time() - start_time), DEFAULT_SLEEP_TIME)
                    if sleep_time > 0:
                        time.sleep(sleep_time)

        logger.error("SSH connection failed after all retries")
        ssh.close()
        return None


# Legacy function for backward compatibility
def connectHost(timeoutloop: int = 5) -> Optional[paramiko.SSHClient]:
    """Legacy SSH connection function. Use SSHConnectionManager instead."""
    ssh_manager = SSHConnectionManager(config.ssh)
    return ssh_manager._connect()

def exec_command(cmd: str, print_cmd: bool = True) -> Union[Optional[int], Tuple[Optional[str], Optional[int]]]:
    """Execute command via SSH with improved error handling."""
    ssh_manager = SSHConnectionManager(config.ssh)

    try:
        with ssh_manager.get_connection() as ssh:
            if print_cmd:
                logger.info(f"Executing command: {cmd}")
                return _exec_command_with_output(ssh, cmd)
            else:
                return _exec_command_silent(ssh, cmd)
    except Exception as e:
        logger.error(f"Error executing command '{cmd}': {e}")
        return None if print_cmd else (None, None)


def _exec_command_with_output(ssh: paramiko.SSHClient, cmd: str) -> Optional[int]:
    """Execute command and stream output in real-time."""
    try:
        stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
        stdout.channel.setblocking(0)
        stderr.channel.setblocking(0)

        while not stdout.channel.exit_status_ready():
            # Use select to wait for data to be available
            rl, wl, xl = select.select([stdout.channel, stderr.channel], [], [], 1.0)
            if stdout.channel in rl:
                line = stdout.readline()
                if line:
                    logger.info(line.strip())

            time.sleep(1)  # Prevent busy-waiting

        exit_status = stdout.channel.recv_exit_status()
        return exit_status
    except Exception as e:
        logger.error(f"Error in command execution: {e}")
        return None


def _exec_command_silent(ssh: paramiko.SSHClient, cmd: str) -> Tuple[Optional[str], Optional[int]]:
    """Execute command and return output without streaming."""
    try:
        stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True)
        output = stdout.read().decode("utf-8")
        exit_status = stdout.channel.recv_exit_status()
        return output.strip(), exit_status
    except Exception as e:
        logger.error(f"Error in silent command execution: {e}")
        return None, None

def disable_definition_by_line(cmake_file: str, line_to_disable: str) -> bool:
    """Disable a definition line in CMakeLists.txt by commenting it out."""
    cmd = f"sed -i '/^{line_to_disable.strip()}/s/^/#/' {cmake_file}"
    output, exit_status = exec_command(cmd, print_cmd=False)

    if exit_status == 0:
        logger.info(f"Successfully disabled the line: {line_to_disable}")
        return True
    else:
        logger.error(f"Failed to disable the line: {line_to_disable}")
        return False


def enable_definition_by_line(cmake_file: str, line_to_enable: str) -> bool:
    """Enable a definition line in CMakeLists.txt by removing comment."""
    cmd = f"sed -i '/^#{line_to_enable.strip()}/s/^#//' {cmake_file}"
    output, exit_status = exec_command(cmd, print_cmd=False)

    if exit_status == 0:
        logger.info(f"Successfully enabled the line: {line_to_enable}")
        return True
    else:
        logger.error(f"Failed to enable the line: {line_to_enable}")
        return False


def enable_or_disable_definitions(video_ip_fw: str) -> None:
    """Enable or disable definitions in CMakeLists.txt based on arguments."""
    cmake_file = f"{video_ip_fw}/CMakeLists.txt"

    if enable_defination:
        enable_definitions = enable_defination.split(',')
        for enable_def in enable_definitions:
            enable_definition_by_line(cmake_file, enable_def)

    if disable_defination:
        disable_definitions = disable_defination.split(',')
        for disable_def in disable_definitions:
            disable_definition_by_line(cmake_file, disable_def)


def upload_file(local_path: str, remote_path: str) -> bool:
    """Upload a file to the remote server via SFTP."""
    logger.info(f"Uploading file from {local_path} to {remote_path}")

    if not os.path.exists(local_path):
        logger.error(f"Local file does not exist: {local_path}")
        return False

    ssh_manager = SSHConnectionManager(config.ssh)

    try:
        with ssh_manager.get_connection() as ssh:
            with ssh.open_sftp() as sftp:
                sftp.put(local_path, remote_path)
                logger.info(f"File uploaded successfully from {local_path} to {remote_path}")
                return True
    except Exception as e:
        logger.error(f"File upload failed: {e}")
        return False

def generate_csv(core_filename: str, metadata: Dict[str, Any], output_filename: str = 'result.csv') -> bool:
    """Generate enhanced CSV with metadata columns."""
    try:
        with open(core_filename, mode='r', newline='') as core_file:
            reader = csv.reader(core_file)
            headers = next(reader)

            metadata_headers = ['DATE', 'videoip_branch', 'Videoip_hash', 'cfg_branch',
                              'cfg_files_hash', 'cfg_file_name', 'test_type', 'withTAV']

            with open(output_filename, mode='w', newline='') as updated_file:
                writer = csv.writer(updated_file)
                writer.writerow(metadata_headers + headers)

                for row in reader:
                    metadata_values = [
                        metadata['DATE'], metadata['videoip_branch'], metadata['Videoip_hash'],
                        metadata['cfg_branch'], metadata['cfg_files_hash'], metadata['cfg_file_name'],
                        metadata['test_type'], metadata['withTAV']
                    ]
                    writer.writerow(metadata_values + row)

        logger.info(f"CSV generated successfully: {output_filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to generate CSV: {e}")
        return False

def store_to_db(path_to_csv: str, table_name: str = 'Results') -> bool:
    """Store CSV data to database."""
    try:
        db = mySQL()
        db.insertCSVData(path_to_csv, table_name)
        logger.info(f"Data stored to database table: {table_name}")
        return True
    except Exception as e:
        logger.error(f"Failed to store data to database: {e}")
        return False


def download_file(remote_file_path: str, local_directory: str) -> bool:
    """Download a file from the remote server via SFTP."""
    ssh_manager = SSHConnectionManager(config.ssh)

    try:
        with ssh_manager.get_connection() as ssh:
            with ssh.open_sftp() as sftp:
                local_file_path = os.path.join(local_directory, os.path.basename(remote_file_path))
                sftp.get(remote_file_path, local_file_path)
                logger.info(f"Downloaded: {remote_file_path} to {local_file_path}")
                return True
    except Exception as e:
        logger.error(f"Download failed: {e}")
        return False

def check_tav(config_file_path: str) -> bool:
    """Check if TAV (AccurateRC) is enabled in the config file."""
    try:
        cmd = f"cat {config_file_path}"
        output, exit_status = exec_command(cmd, print_cmd=False)

        if exit_status == 0 and output:
            for line in output.splitlines():
                line = line.strip()
                if "AccurateRC" in line and "=" in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.split(';')[0].strip()
                    if key == "AccurateRC" and value == "1":
                        logger.info(f"TAV (AccurateRC) is enabled in {config_file_path}")
                        return True
            logger.info(f"TAV (AccurateRC) is not enabled in {config_file_path}")
            return False
        else:
            logger.error(f"Failed to read the file: {config_file_path}")
            return False
    except Exception as e:
        logger.error(f"Error checking TAV in {config_file_path}: {e}")
        return False

def copy_files_to_job(job_dir: str, video_ip_fw: str, num_passes: int) -> bool:
    """Copy required files to job directory for all passes."""
    logger.info("Copying required files to job directory")

    try:
        # Copy files from regression_quick_quality_scripts to job
        quality_scripts_dir = f"{job_dir}/regression_quick_quality_scripts/*"
        exec_command(f"cp -r {quality_scripts_dir} {job_dir}")

        # Copy binaries from simulation directory to job
        exec_command(f"cp -r {config.simulations_dir} {job_dir}")

        # Copy config files
        exec_command(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {job_dir}/quality_cfg/")

        # Copy Gaudi binary if not using custom Gaudi
        if not args.custom_Gaudi:
            exec_command(f"find {video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {job_dir}/Gaudi \;")

        # Copy files for additional passes
        for i in range(1, num_passes):
            pass_dir = os.path.join(job_dir, f"pass{i}")
            exec_command(f"mkdir -p {pass_dir}", print_cmd=False)
            exec_command(f"cp -r {quality_scripts_dir} {pass_dir}")
            exec_command(f"cp -r {config.simulations_dir} {pass_dir}")
            exec_command(f"cp -r {job_dir}/regression_cfg/quick_quality_cfg_files/* {pass_dir}/quality_cfg/")

            if not args.custom_Gaudi:
                exec_command(f"find {video_ip_fw} -type f -name 'Gaudi*' -executable -exec cp {{}} {pass_dir}/Gaudi \;")

        logger.info("Files copied successfully to job directory")
        return True
    except Exception as e:
        logger.error(f"Failed to copy files to job directory: {e}")
        return False


def compare_bdrate(with_TAV,video_ip_branch,config_filename):
	pd.set_option('display.max_rows', None)   # Show all rows
	pd.set_option('display.max_columns', None)  # Show all columns
	pd.set_option('display.width', None)      # Don't wrap lines
	pd.set_option('display.max_colwidth', None)  # Show full content in each column (no truncation)

	withTAV = 1 if with_TAV else 0

	# Define the list of codecs and sequences
	codecs = ['av1', 'h265', 'h264']
	sequences = [
		'CoverSong_4bad_1920x1080p60',
		'Gaming_1080P-277c_1920x1080p60',
		'TVClip_3758_1920x1080p30',
		'WITCHER3_cut_450_849_1920x1080p60',
		'old_town_cross_1920x1080p50',
		'riverbed_1920x1080p25'
	]

	# Fetch data from the database
	query = f"""
	SELECT
		CODEC,
		SEQUENCE,
		MIN(BDRATE_PSNR) AS BDRATE_PSNR,
		MIN(BDRATE_SSIM) AS BDRATE_SSIM,
		MIN(BDRATE_VMAF) AS BDRATE_VMAF
	FROM
		Quality_Results.Results
	WHERE
		withTAV = {withTAV}
		AND videoip_branch = '{video_ip_branch}'
		AND cfg_file_name = '{config_filename}'
	GROUP BY
		CODEC,
		SEQUENCE;
	"""

	try:
		db = mySQL()
		db_data = db.sqlExecute(query)
	except Exception as e:
		print(f"Error fetching data from the database: {e}")
		db_data = pd.DataFrame()
	columns = ['CODEC', 'SEQUENCE', 'BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']
	db_data = pd.DataFrame(db_data, columns=columns)
	for col in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
		db_data[col] = db_data[col].apply(float)

	# Read and filter data from the CSV file
	try:
		csv_data = pd.read_csv('result.csv')
	except Exception as e:
		print(f"Error reading CSV file: {e}")
		csv_data = pd.DataFrame()
	csv_data = csv_data.groupby(['CODEC', 'SEQUENCE']).agg({
		'BDRATE_PSNR': 'min',
		'BDRATE_SSIM': 'min',
		'BDRATE_VMAF': 'min'
	}).reset_index()
	comparison_results = []

	for codec in codecs:
		for sequence in sequences:
			db_row = db_data[(db_data['CODEC'] == codec) & (db_data['SEQUENCE'] == sequence)]
			csv_row = csv_data[(csv_data['CODEC'] == codec) & (csv_data['SEQUENCE'] == sequence)]
			if not db_row.empty and not csv_row.empty:
				# Extract
				db_values = db_row.iloc[0]
				csv_values = csv_row.iloc[0]
				# Compare and store results
				comparison_entry = {
					'CODEC': codec,
					'SEQUENCE': sequence
				}
				for metric in ['BDRATE_PSNR', 'BDRATE_SSIM', 'BDRATE_VMAF']:
					db_value = db_values[metric]
					csv_value = csv_values[metric].round(2)
					diff = round(db_value - csv_value, 2)
					comparison_entry[f'{metric}_db'] = db_value
					comparison_entry[f'{metric}_csv'] = csv_value
					comparison_entry[f'{metric}_diff'] = diff
				comparison_results.append(comparison_entry)
	comparison_df = pd.DataFrame(comparison_results)
	comparison_df = comparison_df.rename(columns={
		'BDRATE_PSNR_db': 'BD_PSNR_db',
		'BDRATE_PSNR_csv': 'BD_PSNR_csv',
		'BDRATE_PSNR_diff': 'BD_PSNR_diff',
		'BDRATE_SSIM_db': 'BD_SSIM_db',
		'BDRATE_SSIM_csv': 'BD_SSIM_csv',
		'BDRATE_SSIM_diff': 'BD_SSIM_diff',
		'BDRATE_VMAF_db': 'BD_VMAF_db',
		'BDRATE_VMAF_csv': 'BD_VMAF_csv',
		'BDRATE_VMAF_diff': 'BD_VMAF_diff'
	})
	print("\n")
	print(comparison_df.to_string(index=False))
	print("\n")
	average_df = comparison_df.groupby('CODEC').agg({
		'BD_PSNR_db': 'mean',
		'BD_PSNR_csv': 'mean',
		'BD_SSIM_db': 'mean',
		'BD_SSIM_csv': 'mean',
		'BD_VMAF_db': 'mean',
		'BD_VMAF_csv': 'mean'
	}).reset_index()

	# Calculate the differences between database and CSV averages
	average_df['BD_PSNR_diff'] = average_df['BD_PSNR_db'] - average_df['BD_PSNR_csv']
	average_df['BD_SSIM_diff'] = average_df['BD_SSIM_db'] - average_df['BD_SSIM_csv']
	average_df['BD_VMAF_diff'] = average_df['BD_VMAF_db'] - average_df['BD_VMAF_csv']

	# Rename the columns for clarity
	average_df = average_df.rename(columns={
		'BD_PSNR_db': 'Avg_BD_PSNR_db',
		'BD_PSNR_csv': 'Avg_BD_PSNR_csv',
		'BD_SSIM_db': 'Avg_BD_SSIM_db',
		'BD_SSIM_csv': 'Avg_BD_SSIM_csv',
		'BD_VMAF_db': 'Avg_BD_VMAF_db',
		'BD_VMAF_csv': 'Avg_BD_VMAF_csv'
	})

	# Reorder the columns to place the diff columns immediately after their corresponding metrics

	average_df = average_df[[
		'CODEC',
		'Avg_BD_PSNR_db', 'Avg_BD_PSNR_csv', 'BD_PSNR_diff',
		'Avg_BD_SSIM_db', 'Avg_BD_SSIM_csv', 'BD_SSIM_diff',
		'Avg_BD_VMAF_db', 'Avg_BD_VMAF_csv', 'BD_VMAF_diff'
	]]

	# Print the new table with average values and differences
	print(average_df.to_string(index=False))

def ensure_job_directory(base_dir: str, job_name: str, max_attempts: int = 100) -> str:
    """Ensures the job directory exists, creating it or incrementing the name if needed."""
    original_job_name = job_name
    counter = 1

    for attempt in range(max_attempts):
        try:
            cmd = "mkdir -p" if "/" in job_name else "mkdir"
            output, exit_code = exec_command(f"cd {base_dir} && {cmd} {job_name}", print_cmd=False)

            if exit_code == 0:
                logger.info(f"Job directory created: {base_dir}/{job_name}")
                return job_name
            else:
                job_name = f"{original_job_name}_{counter}"
                counter += 1
        except Exception as e:
            logger.error(f"Error creating job directory: {e}")
            break

    raise RuntimeError(f"Failed to create job directory after {max_attempts} attempts")


def check_job_status(job_id: str, check_interval: int = DEFAULT_JOB_CHECK_INTERVAL) -> bool:
    """Monitor job status until completion."""
    logger.info(f"Monitoring job {job_id} status")

    while True:
        try:
            cmd = f"squeue -j {job_id}"
            output, exit_code = exec_command(cmd, print_cmd=False)

            if exit_code == 0:
                if job_id not in output:
                    logger.info(f"Job {job_id} has completed")
                    return True
            else:
                logger.warning(f"Failed to check job status for {job_id}")

            time.sleep(check_interval)
        except KeyboardInterrupt:
            logger.info("Job monitoring interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Error checking job status: {e}")
            return False


def git_clone_repository(job_dir: str, branch: str, url: str) -> bool:
    """Clone a git repository to the specified directory."""
    try:
        cmd = f"git clone --depth 1 -b {branch} {url}"
        exit_code = exec_command(f"cd {job_dir} && {cmd}", print_cmd=False)

        if exit_code == 0:
            logger.info(f"Successfully cloned {url} branch {branch}")
            return True
        else:
            logger.error(f"Failed to clone repository {url}")
            return False
    except Exception as e:
        logger.error(f"Error cloning repository: {e}")
        return False


def get_git_hash(repo_dir: str) -> Optional[str]:
    """Get the current git commit hash for a repository."""
    try:
        cmd = f"git -C {repo_dir} rev-parse HEAD"
        output, exit_code = exec_command(cmd, print_cmd=False)

        if exit_code == 0 and output:
            return output.strip()
        else:
            logger.error(f"Failed to get git hash for {repo_dir}")
            return None
    except Exception as e:
        logger.error(f"Error getting git hash: {e}")
        return None


def execute_command_in_directory(job_dir: str, cmd: str) -> bool:
    """Execute a command in a specific directory with error handling."""
    try:
        full_cmd = f"cd {job_dir} && {cmd}"
        exit_code = exec_command(full_cmd)

        if exit_code == 0:
            return True
        else:
            logger.error(f"Command failed in {job_dir}: {cmd}")
            return False
    except Exception as e:
        logger.error(f"Error executing command: {e}")
        return False


def build_and_compile_gaudi(video_ip_fw: str) -> bool:
    """Build and compile Gaudi with proper error handling."""
    logger.info("Starting Gaudi compilation")

    try:
        build_cmd = f'cd {video_ip_fw}/build && chmod 777 *.sh && ./cmake.sh {compile_flag}'
        exit_status = exec_command(build_cmd, print_cmd=False)

        if exit_status == 0:
            logger.info("Gaudi compiled successfully")
            return True
        else:
            logger.error("Gaudi compilation failed")
            return False
    except Exception as e:
        logger.error(f"Error during Gaudi compilation: {e}")
        return False


# Legacy function names for backward compatibility
def git_steps(job_dir: str, branch: str, url: str) -> bool:
    """Legacy function. Use git_clone_repository instead."""
    return git_clone_repository(job_dir, branch, url)


def get_hash(repo_dir: str) -> Optional[str]:
    """Legacy function. Use get_git_hash instead."""
    return get_git_hash(repo_dir)


def running_cmds(job_dir: str, cmd: str) -> None:
    """Legacy function. Use execute_command_in_directory instead."""
    if not execute_command_in_directory(job_dir, cmd):
        sys.exit(1)


def Build_and_compile_gaudi(video_ip_fw: str) -> None:
    """Legacy function. Use build_and_compile_gaudi instead."""
    if not build_and_compile_gaudi(video_ip_fw):
        logger.error("Gaudi compilation failed")

total_passes = count_passes()

if args.twopass:
	final_config = next(config for config in [config5, config4, config3, config2, config1] if config != "NONE.cfg")
else:
	final_config = config1
def report_generation() -> bool:
    """Main function to generate video quality reports."""
    logger.info(f"Starting report generation - Host: {config.ssh.host}, User: {config.ssh.username}")

    if total_passes == 0:
        logger.error("No config file selected")
        return False

    try:
        # Setup job directory
        job_name = ensure_job_directory(config.base_dir, args.job)
        job_dir = f"{config.base_dir}/{job_name}"
        logger.info(f"Job directory: {job_dir}")

        # Repository paths
        video_ip_fw = f"{job_dir}/video_ip_ranger"

        # Clone repositories
        logger.info("Checking out regression_quick_quality_scripts branch")
        if not git_clone_repository(job_dir, args.branch, config.git.regression_scripts_url):
            logger.error("Failed to clone regression scripts")
            return False

        if not args.custom_Gaudi:
            logger.info("Checking out video_ip branch to compile Gaudi")
            if not git_clone_repository(job_dir, args.gaudibranch, config.git.video_ip_url):
                logger.error("Failed to clone video IP repository")
                return False

            enable_or_disable_definitions(video_ip_fw)
            if not build_and_compile_gaudi(video_ip_fw):
                logger.error("Failed to compile Gaudi")
                return False

        logger.info("Checking out CFG branch to get config files")
        if not git_clone_repository(job_dir, args.configbranch, config.git.regression_cfg_url):
            logger.error("Failed to clone config repository")
            return False

        if not copy_files_to_job(job_dir, video_ip_fw, total_passes):
            logger.error("Failed to copy files to job directory")
            return False
		print("\n")
		with open("logPathSanity.txt", "a") as f:
			for i in range(total_passes):
				config = globals()[f"config{i+1}"]
				job_pass_name = job_name if i == total_passes - 1 else os.path.join(job_name, f"pass{i+1}")
				job_pass_dir = os.path.join(base_dir, job_pass_name)
				upload_file(f"{config}",f"{job_pass_dir}/quality_cfg/{config}")
				if args.custom_Gaudi:
					upload_file(f"Gaudi",f"{job_pass_dir}/Gaudi")
					output, exit_status = exec_command(f"chmod +x {job_pass_dir}/Gaudi",print_cmd=False)
				cmd = f"python3 gaudi_quality.py -p {config} -j {job_pass_name} -t {job_name}"
				if args.twopass:
					cmd += " --encoding_only" if i != total_passes - 1 else ""
				print(cmd)
				f.write(f"\nUse Windows file explorer to open log files of jenkins at below link for pass{i+1}:\n\n")
				f.write('\\\\************\\video-quality-evaluation-testResult\\quick_quality_test\\{}\n'.format(job_pass_name))

				f.write("\nUse Linux file explorer to open log files of jenkins at below link:\n\n")
				f.write('//************/video-quality-evaluation-testResult/quick_quality_test/{}\n'.format(job_pass_name))

				exit_code = exec_command(f"cd {job_pass_dir} && {cmd}")

				if exit_code == 10:
					print_now(f"Config file validation failed")
					sys.exit(1)
				elif exit_code != 0:
					print_now(f"Failed to execute commands {cmd}")
					sys.exit(1)
		cmake_file = f"{Video_ip_fw}/CMakeLists.txt"
		final_config_path = f"{job_name}/quality_cfg/{final_config}"
		withTAV = check_tav(final_config_path)
		remote_file_path = f"{job_dir}/data.csv"
		local_directory = os.getcwd()
		download_file(remote_file_path,local_directory)
		today_date = datetime.date.today().isoformat()
		videoip_tag = args.gaudibranch
		videoip_hash = get_hash(Video_ip_fw)
		cfg_branch = args.configbranch
		cfgbranch_hash = get_hash(f"{job_dir}/regression_cfg")
		cfg_key = 'config1' if not args.twopass else f"config{total_passes}"
		cfg_file = globals().get(cfg_key, None)
		test_type = "1Pass" if not args.twopass else "2Pass"
		metadata = {
			'DATE': today_date,
			'videoip_branch': videoip_tag,
			'Videoip_hash': videoip_hash,
			'cfg_branch': cfg_branch,
			'cfg_files_hash': cfgbranch_hash,
			'cfg_file_name': cfg_file,
			'test_type': test_type,
			'withTAV': withTAV
		}
		core_file = f"{local_directory}/data.csv"
		generate_csv(core_file,metadata)
		if args.save_to_db:
			store_to_db(f"{local_directory}/result.csv")
		compare_bdrate(withTAV,args.Compare_with,args.config_file)
	finally:
		os.makedirs("archive")
		getoutput(f"mv data.csv archive/")
		getoutput(f"mv logPathSanity.txt archive/")



if __name__ == "__main__":
	report_generation()