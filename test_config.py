#!/usr/bin/env python3
"""
Simple test script to verify the configuration system works.
"""

import sys
import os

# Add the current directory to Python path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """Test that configuration loading works properly."""
    try:
        # Import the functions we need
        from top_level_videoquality import load_config, create_sample_config, get_config
        
        print("✅ Successfully imported configuration functions")
        
        # Test creating a sample config
        print("\n📝 Testing sample config creation...")
        create_sample_config('test_config.json')
        
        if os.path.exists('test_config.json'):
            print("✅ Sample config file created successfully")
            
            # Test loading the config
            print("\n🔧 Testing config loading...")
            config = load_config()
            print(f"✅ Configuration loaded successfully")
            print(f"   SSH Host: {config.ssh.host}")
            print(f"   SSH User: {config.ssh.username}")
            print(f"   Base Dir: {config.base_dir}")
            
            # Clean up
            os.remove('test_config.json')
            print("🧹 Cleaned up test config file")
            
        else:
            print("❌ Failed to create sample config file")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Video Quality Configuration System")
    print("=" * 50)
    
    success = test_config_loading()
    
    if success:
        print("\n🎉 All configuration tests passed!")
        print("\n📋 Next steps:")
        print("1. Run: python top_level_videoquality.py --create-config")
        print("2. Edit the generated config.json file with your credentials")
        print("3. Run your normal commands")
    else:
        print("\n💥 Configuration tests failed!")
        sys.exit(1)
