# Configuration Fix Guide

## 🔧 Quick Fix for Configuration Issues

I've identified and fixed the configuration loading issues. Here's what was wrong and how it's now fixed:

### **Problems Fixed:**

1. **❌ Config loaded too early**: Configuration was being loaded before argument parsing
2. **❌ Function order issues**: `create_sample_config()` was called before being defined
3. **❌ Global config references**: Multiple places referenced `config` directly instead of using a getter

### **✅ Solutions Implemented:**

1. **Lazy Configuration Loading**: Config is now loaded only when needed
2. **Proper Initialization Order**: Functions are called in the correct order
3. **Safe Config Access**: All config access goes through `get_config()` function

## 🚀 How to Use the Fixed Configuration

### **1. Create Configuration File**
```bash
# Generate a sample config file
python top_level_videoquality.py --create-config
```

This creates a `config.json` file like this:
```json
{
    "ssh": {
        "host": "************",
        "username": "nvme",
        "password": "your_password_here",
        "timeout": 60,
        "retries": 5,
        "keepalive": 30
    },
    "git": {
        "regression_scripts_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/quality_tests/regression_quick_quality_scripts.git",
        "video_ip_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/video_ip_ranger.git",
        "regression_cfg_url": "https://git.video:'r65j%4!oBUma'@git.netint.ca/vit/regression_cfg.git"
    },
    "base_dir": "/mnt/ceph/Automation/quick_quality_test",
    "simulations_dir": "/mnt/ceph/Automation/simulations/*"
}
```

### **2. Edit Configuration**
```bash
# Edit the config file with your actual credentials
nano config.json
# or
vim config.json
```

**Minimal config (only override what you need):**
```json
{
    "ssh": {
        "password": "your_actual_password"
    }
}
```

### **3. Run Normally**
```bash
# The script will automatically detect and use config.json
python top_level_videoquality.py -j job1 -c1 config1.cfg
```

## 🧪 Test the Configuration

I've created a test script to verify everything works:

```bash
# Test the configuration system
python test_config.py
```

This will:
- ✅ Test config file creation
- ✅ Test config loading
- ✅ Verify all functions work
- ✅ Clean up test files

## 🐳 Container Usage

### **Docker Volume Mount:**
```bash
# Mount your config file into the container
docker run -v /path/to/your/config.json:/app/config.json your-image \
  python top_level_videoquality.py -j job1 -c1 config1.cfg
```

### **Kubernetes ConfigMap:**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: video-quality-config
data:
  config.json: |
    {
      "ssh": {
        "password": "your_password"
      }
    }
```

### **Docker Compose:**
```yaml
version: '3.8'
services:
  video-quality:
    image: your-image
    volumes:
      - ./config.json:/app/config.json
    command: python top_level_videoquality.py -j job1 -c1 config1.cfg
```

## 🔄 Configuration Loading Priority

The script looks for configuration in this order:

1. **`config.json`** (current directory)
2. **`video_quality_config.json`** (current directory)  
3. **`/app/config.json`** (container standard location)
4. **Environment variables** (fallback)
5. **Built-in defaults** (final fallback)

## 🔒 Security Best Practices

### **For Development:**
```json
{
    "ssh": {
        "password": "dev_password"
    }
}
```

### **For Production (using secrets):**
```bash
# Create config template
echo '{"ssh":{"password":"REPLACE_ME"}}' > config.json

# Replace at runtime
sed -i 's/REPLACE_ME/'$SSH_PASSWORD'/g' config.json
```

### **Using Environment Variables (fallback):**
```bash
export SSH_PASSWORD="your_password"
# Script will use env vars if no config file found
```

## 🆘 Troubleshooting

### **"Config not working" Issues:**

1. **Check if config file exists:**
   ```bash
   ls -la config.json
   ```

2. **Validate JSON syntax:**
   ```bash
   python -m json.tool config.json
   ```

3. **Test configuration loading:**
   ```bash
   python test_config.py
   ```

4. **Check file permissions:**
   ```bash
   chmod 644 config.json
   ```

### **Container Issues:**

1. **Verify config is mounted:**
   ```bash
   docker exec -it container_name ls -la /app/config.json
   ```

2. **Check container logs:**
   ```bash
   docker logs container_name
   ```

### **Permission Issues:**
```bash
# Make sure the script can read the config
chmod +r config.json

# Make sure the script is executable
chmod +x top_level_videoquality.py
```

## ✅ Verification Steps

1. **Create config:**
   ```bash
   python top_level_videoquality.py --create-config
   ```

2. **Edit config:**
   ```bash
   # Update password in config.json
   ```

3. **Test config:**
   ```bash
   python test_config.py
   ```

4. **Run script:**
   ```bash
   python top_level_videoquality.py -j test_job -c1 test_config.cfg
   ```

The configuration system is now robust and container-friendly! 🎉
